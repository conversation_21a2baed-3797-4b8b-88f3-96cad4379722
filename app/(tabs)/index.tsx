import React, { useState, useEffect } from 'react';
import { Alert } from 'react-native';
import * as Location from 'expo-location';

import { Container, Card, Row, Column } from '@/components/StyledView';
import { TitleText, BodyText, CaptionText } from '@/components/StyledText';
import MapComponent from '@/components/MapComponent';
import { useAlarmStore } from '@/store/alarmStore';
import { LocationCoordinates, MapRegion } from '@/types';

export default function MapScreen() {
  const { alarms, addAlarm, settings } = useAlarmStore();
  const [mapRegion, setMapRegion] = useState<MapRegion>({
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  const [selectedLocation, setSelectedLocation] = useState<LocationCoordinates | null>(null);

  useEffect(() => {
    getCurrentLocation();
  }, []);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Location permission is required to use this app.');
        return;
      }

      const currentLocation = await Location.getCurrentPositionAsync({});
      const coords = {
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
      };

      setMapRegion({
        ...coords,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert('Error', 'Failed to get current location.');
    }
  };

  const handleMapPress = (event: any) => {
    const coordinate = event.nativeEvent.coordinate;
    setSelectedLocation(coordinate);
  };

  const handleCreateAlarm = () => {
    if (!selectedLocation) {
      Alert.alert('No Location Selected', 'Please tap on the map to select a location for your alarm.');
      return;
    }

    Alert.prompt(
      'Create Alarm',
      'Enter a name for this alarm:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Create',
          onPress: (name) => {
            if (name && name.trim()) {
              addAlarm({
                name: name.trim(),
                latitude: selectedLocation.latitude,
                longitude: selectedLocation.longitude,
                radius: settings.defaultRadius,
                isActive: true,
                soundUri: settings.defaultSound,
              });
              setSelectedLocation(null);
              Alert.alert('Success', 'Alarm created successfully!');
            }
          },
        },
      ],
      'plain-text',
      'My Alarm'
    );
  };

  return (
    <Container>
      <MapComponent
        region={mapRegion}
        alarms={alarms}
        selectedLocation={selectedLocation}
        defaultRadius={settings.defaultRadius}
        onRegionChange={setMapRegion}
        onMapPress={handleMapPress}
      />

      {/* Instructions */}
      {!selectedLocation && alarms.length === 0 && (
        <Card
          position="absolute"
          top={16}
          left={16}
          right={16}
          backgroundColor="white"
        >
          <TitleText>Welcome to Snozbuz!</TitleText>
          <BodyText marginTop="s">
            Tap anywhere on the map to create a location-based alarm.
          </BodyText>
        </Card>
      )}

      {/* Create alarm button */}
      {selectedLocation && (
        <Card
          position="absolute"
          bottom={32}
          left={16}
          right={16}
          backgroundColor="primary"
        >
          <Row justifyContent="space-between" alignItems="center">
            <Column flex={1}>
              <TitleText color="white">Create Alarm Here?</TitleText>
              <CaptionText color="white" marginTop="xs">
                Radius: {settings.defaultRadius}m
              </CaptionText>
            </Column>
            <Row>
              <BodyText
                color="white"
                marginRight="l"
                onPress={() => setSelectedLocation(null)}
              >
                Cancel
              </BodyText>
              <BodyText
                color="white"
                fontWeight="bold"
                onPress={handleCreateAlarm}
              >
                Create
              </BodyText>
            </Row>
          </Row>
        </Card>
      )}
    </Container>
  );
}
