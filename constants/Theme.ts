import { createTheme } from '@shopify/restyle';

const palette = {
  // Primary colors
  primary: '#007AFF',
  primaryDark: '#0056CC',
  primaryLight: '#4DA3FF',
  
  // Secondary colors
  secondary: '#FF9500',
  secondaryDark: '#CC7700',
  secondaryLight: '#FFB84D',
  
  // Neutral colors
  white: '#FFFFFF',
  black: '#000000',
  gray50: '#F9FAFB',
  gray100: '#F3F4F6',
  gray200: '#E5E7EB',
  gray300: '#D1D5DB',
  gray400: '#9CA3AF',
  gray500: '#6B7280',
  gray600: '#4B5563',
  gray700: '#374151',
  gray800: '#1F2937',
  gray900: '#111827',
  
  // Status colors
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  
  // Background colors
  backgroundLight: '#FFFFFF',
  backgroundDark: '#000000',
  surfaceLight: '#F9FAFB',
  surfaceDark: '#1F2937',
};

const theme = createTheme({
  colors: {
    // Main colors
    primary: palette.primary,
    primaryDark: palette.primaryDark,
    primaryLight: palette.primaryLight,
    secondary: palette.secondary,
    secondaryDark: palette.secondaryDark,
    secondaryLight: palette.secondaryLight,
    
    // Text colors
    text: palette.gray900,
    textSecondary: palette.gray600,
    textLight: palette.gray400,
    textInverse: palette.white,
    
    // Background colors
    background: palette.backgroundLight,
    surface: palette.surfaceLight,
    card: palette.white,
    
    // Border colors
    border: palette.gray200,
    borderLight: palette.gray100,
    
    // Status colors
    success: palette.success,
    warning: palette.warning,
    error: palette.error,
    info: palette.info,
    
    // Neutral colors
    white: palette.white,
    black: palette.black,
    transparent: 'transparent',
  },
  spacing: {
    xs: 4,
    s: 8,
    m: 16,
    l: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadii: {
    xs: 4,
    s: 8,
    m: 12,
    l: 16,
    xl: 24,
    round: 999,
  },
  textVariants: {
    header: {
      fontSize: 32,
      fontWeight: 'bold',
      color: 'text',
    },
    title: {
      fontSize: 24,
      fontWeight: '600',
      color: 'text',
    },
    subtitle: {
      fontSize: 20,
      fontWeight: '500',
      color: 'text',
    },
    body: {
      fontSize: 16,
      fontWeight: '400',
      color: 'text',
    },
    caption: {
      fontSize: 14,
      fontWeight: '400',
      color: 'textSecondary',
    },
    small: {
      fontSize: 12,
      fontWeight: '400',
      color: 'textLight',
    },
    button: {
      fontSize: 16,
      fontWeight: '600',
      color: 'white',
    },
  },
  buttonVariants: {
    primary: {
      backgroundColor: 'primary',
      borderRadius: 'm',
      paddingVertical: 'm',
      paddingHorizontal: 'l',
    },
    secondary: {
      backgroundColor: 'secondary',
      borderRadius: 'm',
      paddingVertical: 'm',
      paddingHorizontal: 'l',
    },
    outline: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: 'border',
      borderRadius: 'm',
      paddingVertical: 'm',
      paddingHorizontal: 'l',
    },
  },
  cardVariants: {
    default: {
      backgroundColor: 'card',
      borderRadius: 'm',
      padding: 'm',
      shadowColor: 'black',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    elevated: {
      backgroundColor: 'card',
      borderRadius: 'l',
      padding: 'l',
      shadowColor: 'black',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 6,
    },
  },
});

export type Theme = typeof theme;
export default theme;
